using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ISystemExerciseService
    {
        IDataResult<List<SystemExerciseDto>> GetAllSystemExercises();
        IDataResult<List<SystemExerciseDto>> GetSystemExercisesByCategory(int categoryId);
        IDataResult<PaginatedResult<SystemExerciseDto>> GetSystemExercisesFiltered(SystemExerciseFilterDto filter);
        IDataResult<List<SystemExerciseDto>> SearchSystemExercises(string searchTerm);
        IDataResult<SystemExerciseDto> GetSystemExerciseDetail(int exerciseId);
        IDataResult<SystemExerciseDto> GetById(int exerciseId);
        IDataResult<List<SystemExerciseDto>> GetPopularSystemExercises();
        IDataResult<List<SystemExerciseDto>> GetRecommendedSystemExercises();
        IResult Add(SystemExerciseAddDto exerciseAddDto);
        IResult Update(SystemExerciseUpdateDto exerciseUpdateDto);
        IResult Delete(int exerciseId);
    }
}

using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class SystemExerciseManager : ISystemExerciseService
    {
        ISystemExerciseDal _systemExerciseDal;

        public SystemExerciseManager(ISystemExerciseDal systemExerciseDal)
        {
            _systemExerciseDal = systemExerciseDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetAllSystemExercises()
        {
            var result = _systemExerciseDal.GetAllSystemExercises();
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetSystemExercisesByCategory(int categoryId)
        {
            var result = _systemExerciseDal.GetSystemExercisesByCategory(categoryId);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<SystemExerciseDto>> GetSystemExercisesFiltered(SystemExerciseFilterDto filter)
        {
            var result = _systemExerciseDal.GetSystemExercisesFiltered(filter);
            return new SuccessDataResult<PaginatedResult<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> SearchSystemExercises(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<SystemExerciseDto>>("Arama terimi boş olamaz.");
            }

            var result = _systemExerciseDal.SearchSystemExercises(searchTerm);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<SystemExerciseDto> GetSystemExerciseDetail(int exerciseId)
        {
            var result = _systemExerciseDal.GetSystemExerciseDetail(exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }

            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<SystemExerciseDto> GetById(int exerciseId)
        {
            var exercise = _systemExerciseDal.Get(e => e.SystemExerciseID == exerciseId);
            if (exercise == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }

            var result = new SystemExerciseDto
            {
                SystemExerciseID = exercise.SystemExerciseID,
                ExerciseCategoryID = exercise.ExerciseCategoryID,
                ExerciseName = exercise.ExerciseName,
                Description = exercise.Description,
                Instructions = exercise.Instructions,
                MuscleGroups = exercise.MuscleGroups,
                Equipment = exercise.Equipment,
                DifficultyLevel = exercise.DifficultyLevel,
                DifficultyLevelText = exercise.DifficultyLevel == 1 ? "Başlangıç" :
                                    exercise.DifficultyLevel == 2 ? "Orta" :
                                    exercise.DifficultyLevel == 3 ? "İleri" : "",
                PopularityLevel = exercise.PopularityLevel,
                PopularityLevelText = exercise.PopularityLevel == 1 ? "Temel" :
                                    exercise.PopularityLevel == 2 ? "Popüler" :
                                    exercise.PopularityLevel == 3 ? "İleri" : "",
                DisplayOrder = exercise.DisplayOrder,
                IsRecommended = exercise.IsRecommended,
                UsageCount = exercise.UsageCount,
                IsActive = exercise.IsActive,
                CreationDate = exercise.CreationDate
            };

            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [CacheAspect(60)] // 60 dakika cache
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetPopularSystemExercises()
        {
            var result = _systemExerciseDal.GetPopularSystemExercises();
            return new SuccessDataResult<List<SystemExerciseDto>>(result, "Popüler egzersizler başarıyla getirildi.");
        }

        [CacheAspect(60)] // 60 dakika cache
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetRecommendedSystemExercises()
        {
            var result = _systemExerciseDal.GetRecommendedSystemExercises();
            return new SuccessDataResult<List<SystemExerciseDto>>(result, "Önerilen egzersizler başarıyla getirildi.");
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi ekleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(SystemExerciseAddDto exerciseAddDto)
        {
            var exercise = new SystemExercise
            {
                ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                ExerciseName = exerciseAddDto.ExerciseName,
                Description = exerciseAddDto.Description,
                Instructions = exerciseAddDto.Instructions,
                MuscleGroups = exerciseAddDto.MuscleGroups,
                Equipment = exerciseAddDto.Equipment,
                DifficultyLevel = exerciseAddDto.DifficultyLevel,
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _systemExerciseDal.Add(exercise);
            return new SuccessResult("Sistem egzersizi başarıyla eklendi.");
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi güncelleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(SystemExerciseUpdateDto exerciseUpdateDto)
        {
            var existingExercise = _systemExerciseDal.Get(e => e.SystemExerciseID == exerciseUpdateDto.SystemExerciseID);
            if (existingExercise == null)
            {
                return new ErrorResult("Sistem egzersizi bulunamadı.");
            }

            existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
            existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
            existingExercise.Description = exerciseUpdateDto.Description;
            existingExercise.Instructions = exerciseUpdateDto.Instructions;
            existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
            existingExercise.Equipment = exerciseUpdateDto.Equipment;
            existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
            existingExercise.IsActive = exerciseUpdateDto.IsActive;
            existingExercise.UpdatedDate = DateTime.Now;

            _systemExerciseDal.Update(existingExercise);
            return new SuccessResult("Sistem egzersizi başarıyla güncellendi.");
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi silebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int exerciseId)
        {
            var exercise = _systemExerciseDal.Get(e => e.SystemExerciseID == exerciseId);
            if (exercise == null)
            {
                return new ErrorResult("Sistem egzersizi bulunamadı.");
            }

            // Soft delete
            exercise.IsActive = false;
            exercise.DeletedDate = DateTime.Now;

            _systemExerciseDal.Update(exercise);
            return new SuccessResult("Sistem egzersizi başarıyla silindi.");
        }
    }
}

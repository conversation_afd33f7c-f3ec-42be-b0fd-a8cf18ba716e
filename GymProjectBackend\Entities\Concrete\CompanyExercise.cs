using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class CompanyExercise : ICompanyEntity
    {
        [Key]
        public int CompanyExerciseID { get; set; }
        public int CompanyID { get; set; }
        public int ExerciseCategoryID { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; } // Hedeflenen kas grupları (JSON veya virgülle ayrılmış)
        public string? Equipment { get; set; } // Gerekli ekipman
        public byte? DifficultyLevel { get; set; } // 1: Başlangıç, 2: Orta, 3: İleri
        public byte? PopularityLevel { get; set; } // 1: <PERSON><PERSON>, 2: <PERSON><PERSON><PERSON>, 3: İleri
        public int? DisplayOrder { get; set; } // Sıralama için
        public bool? IsRecommended { get; set; } // Önerilen mi?
        public int? UsageCount { get; set; } // Kullanım sayısı (ileride)
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}

using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class SystemExerciseDto : IDto
    {
        public int SystemExerciseID { get; set; }
        public int ExerciseCategoryID { get; set; }
        public string CategoryName { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
        public string? DifficultyLevelText { get; set; } // "<PERSON><PERSON>langıç", "Orta", "İleri"
        public byte? PopularityLevel { get; set; }
        public string? PopularityLevelText { get; set; } // "Temel", "Popüler", "İleri"
        public int? DisplayOrder { get; set; }
        public bool? IsRecommended { get; set; }
        public int? UsageCount { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    public class SystemExerciseAddDto : IDto
    {
        public int ExerciseCategoryID { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
    }

    public class SystemExerciseUpdateDto : IDto
    {
        public int SystemExerciseID { get; set; }
        public int ExerciseCategoryID { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
        public bool? IsActive { get; set; }
    }

    public class SystemExerciseFilterDto : IDto
    {
        public int? ExerciseCategoryID { get; set; }
        public string? SearchTerm { get; set; }
        public byte? DifficultyLevel { get; set; }
        public byte? PopularityLevel { get; set; }
        public bool? IsRecommended { get; set; }
        public string? Equipment { get; set; }
        public bool? IsActive { get; set; }
        public string? SortBy { get; set; } = "popularity"; // "popularity", "name", "difficulty"
        public string? SortOrder { get; set; } = "asc"; // "asc", "desc"
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}

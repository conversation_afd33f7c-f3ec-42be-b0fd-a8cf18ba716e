using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CompanyExercisesController : ControllerBase
    {
        ICompanyExerciseService _companyExerciseService;

        public CompanyExercisesController(ICompanyExerciseService companyExerciseService)
        {
            _companyExerciseService = companyExerciseService;
        }

        /// <summary>
        /// Salon egzersizlerini getirir
        /// </summary>
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _companyExerciseService.GetCompanyExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kategoriye göre salon egzersizlerini getirir
        /// </summary>
        [HttpGet("getbycategory/{categoryId}")]
        public IActionResult GetByCategory(int categoryId)
        {
            var result = _companyExerciseService.GetCompanyExercisesByCategory(categoryId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Filtrelenmiş salon egzersizlerini sayfalı olarak getirir
        /// </summary>
        [HttpPost("getfiltered")]
        public IActionResult GetFiltered([FromBody] CompanyExerciseFilterDto filter)
        {
            var result = _companyExerciseService.GetCompanyExercisesFiltered(filter);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Salon egzersizlerinde arama yapar
        /// </summary>
        [HttpGet("search")]
        public IActionResult Search([FromQuery] string searchTerm)
        {
            var result = _companyExerciseService.SearchCompanyExercises(searchTerm);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// ID'ye göre salon egzersizi detayını getirir
        /// </summary>
        [HttpGet("getdetail/{id}")]
        public IActionResult GetDetail(int id)
        {
            var result = _companyExerciseService.GetCompanyExerciseDetail(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// ID'ye göre salon egzersizi getirir
        /// </summary>
        [HttpGet("getbyid/{id}")]
        public IActionResult GetById(int id)
        {
            var result = _companyExerciseService.GetById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Yeni salon egzersizi ekler
        /// </summary>
        [HttpPost("add")]
        public IActionResult Add([FromBody] CompanyExerciseAddDto exerciseAddDto)
        {
            var result = _companyExerciseService.Add(exerciseAddDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Salon egzersizini günceller
        /// </summary>
        [HttpPost("update")]
        public IActionResult Update([FromBody] CompanyExerciseUpdateDto exerciseUpdateDto)
        {
            var result = _companyExerciseService.Update(exerciseUpdateDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Salon egzersizini siler
        /// </summary>
        [HttpDelete("delete/{id}")]
        public IActionResult Delete(int id)
        {
            var result = _companyExerciseService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Birleşik egzersiz listesini getirir (Sistem + Salon egzersizleri)
        /// </summary>
        [HttpGet("getcombined")]
        public IActionResult GetCombined()
        {
            var result = _companyExerciseService.GetCombinedExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kategoriye göre birleşik egzersiz listesini getirir
        /// </summary>
        [HttpGet("getcombinedbycategory/{categoryId}")]
        public IActionResult GetCombinedByCategory(int categoryId)
        {
            var result = _companyExerciseService.GetCombinedExercisesByCategory(categoryId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Filtrelenmiş birleşik egzersiz listesini sayfalı olarak getirir
        /// </summary>
        [HttpPost("getcombinedfiltered")]
        public IActionResult GetCombinedFiltered([FromBody] SystemExerciseFilterDto filter)
        {
            var result = _companyExerciseService.GetCombinedExercisesFiltered(filter);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Popüler birleşik egzersizleri getirir (varsayılan görünüm için)
        /// </summary>
        [HttpGet("getcombinedpopular")]
        public IActionResult GetCombinedPopular()
        {
            var result = _companyExerciseService.GetCombinedPopularExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Önerilen birleşik egzersizleri getirir
        /// </summary>
        [HttpGet("getcombinedrecommended")]
        public IActionResult GetCombinedRecommended()
        {
            var result = _companyExerciseService.GetCombinedRecommendedExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}

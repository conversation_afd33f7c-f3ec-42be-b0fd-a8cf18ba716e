using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class SystemExercisesController : ControllerBase
    {
        ISystemExerciseService _systemExerciseService;

        public SystemExercisesController(ISystemExerciseService systemExerciseService)
        {
            _systemExerciseService = systemExerciseService;
        }

        /// <summary>
        /// Tüm sistem egzersizlerini getirir
        /// </summary>
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _systemExerciseService.GetAllSystemExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kategoriye göre sistem egzersizlerini getirir
        /// </summary>
        [HttpGet("getbycategory/{categoryId}")]
        public IActionResult GetByCategory(int categoryId)
        {
            var result = _systemExerciseService.GetSystemExercisesByCategory(categoryId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Filtrelenmiş sistem egzersizlerini sayfalı olarak getirir
        /// </summary>
        [HttpPost("getfiltered")]
        public IActionResult GetFiltered([FromBody] SystemExerciseFilterDto filter)
        {
            var result = _systemExerciseService.GetSystemExercisesFiltered(filter);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Sistem egzersizlerinde arama yapar
        /// </summary>
        [HttpGet("search")]
        public IActionResult Search([FromQuery] string searchTerm)
        {
            var result = _systemExerciseService.SearchSystemExercises(searchTerm);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// ID'ye göre sistem egzersizi detayını getirir
        /// </summary>
        [HttpGet("getdetail/{id}")]
        public IActionResult GetDetail(int id)
        {
            var result = _systemExerciseService.GetSystemExerciseDetail(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// ID'ye göre sistem egzersizi getirir
        /// </summary>
        [HttpGet("getbyid/{id}")]
        public IActionResult GetById(int id)
        {
            var result = _systemExerciseService.GetById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Popüler sistem egzersizlerini getirir (varsayılan görünüm için)
        /// </summary>
        [HttpGet("getpopular")]
        [AllowAnonymous] // Popüler egzersizler herkese açık
        public IActionResult GetPopular()
        {
            var result = _systemExerciseService.GetPopularSystemExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Önerilen sistem egzersizlerini getirir
        /// </summary>
        [HttpGet("getrecommended")]
        [AllowAnonymous] // Önerilen egzersizler herkese açık
        public IActionResult GetRecommended()
        {
            var result = _systemExerciseService.GetRecommendedSystemExercises();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Yeni sistem egzersizi ekler (Sadece owner)
        /// </summary>
        [HttpPost("add")]
        public IActionResult Add([FromBody] SystemExerciseAddDto exerciseAddDto)
        {
            var result = _systemExerciseService.Add(exerciseAddDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Sistem egzersizini günceller (Sadece owner)
        /// </summary>
        [HttpPost("update")]
        public IActionResult Update([FromBody] SystemExerciseUpdateDto exerciseUpdateDto)
        {
            var result = _systemExerciseService.Update(exerciseUpdateDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Sistem egzersizini siler (Sadece owner)
        /// </summary>
        [HttpDelete("delete/{id}")]
        public IActionResult Delete(int id)
        {
            var result = _systemExerciseService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}

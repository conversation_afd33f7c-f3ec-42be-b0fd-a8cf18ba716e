-- Egzersiz Sistemi Migration Script
-- Bu script egzersiz kategorileri ve egzersiz tablolarını oluşturur
-- SystemExercises: Sistem geneli egzersizler
-- CompanyExercises: Salon özel egzersizler

USE [GymProject]
GO

-- 1. ExerciseCategories Tablosu
CREATE TABLE [dbo].[ExerciseCategories](
    [ExerciseCategoryID] [int] IDENTITY(1,1) NOT NULL,
    [CategoryName] [nvarchar](100) NOT NULL,
    [Description] [nvarchar](500) NULL,
    [IsActive] [bit] NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_ExerciseCategories] PRIMARY KEY CLUSTERED ([ExerciseCategoryID] ASC)
)
GO

-- 2. SystemExercises Tablosu (Sistem geneli egzersizler)
CREATE TABLE [dbo].[SystemExercises](
    [SystemExerciseID] [int] IDENTITY(1,1) NOT NULL,
    [ExerciseCategoryID] [int] NOT NULL,
    [ExerciseName] [nvarchar](200) NOT NULL,
    [Description] [nvarchar](1000) NULL,
    [Instructions] [nvarchar](2000) NULL,
    [MuscleGroups] [nvarchar](500) NULL,
    [Equipment] [nvarchar](200) NULL,
    [DifficultyLevel] [tinyint] NULL, -- 1: Başlangıç, 2: Orta, 3: İleri
    [PopularityLevel] [tinyint] NULL DEFAULT(2), -- 1: Temel, 2: Popüler, 3: İleri
    [DisplayOrder] [int] NULL DEFAULT(999), -- Sıralama için
    [IsRecommended] [bit] NULL DEFAULT(0), -- Önerilen mi?
    [UsageCount] [int] NULL DEFAULT(0), -- Kullanım sayısı (ileride)
    [IsActive] [bit] NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_SystemExercises] PRIMARY KEY CLUSTERED ([SystemExerciseID] ASC),
    CONSTRAINT [FK_SystemExercises_ExerciseCategories] FOREIGN KEY([ExerciseCategoryID]) 
        REFERENCES [dbo].[ExerciseCategories] ([ExerciseCategoryID])
)
GO

-- 3. CompanyExercises Tablosu (Salon özel egzersizler)
CREATE TABLE [dbo].[CompanyExercises](
    [CompanyExerciseID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [ExerciseCategoryID] [int] NOT NULL,
    [ExerciseName] [nvarchar](200) NOT NULL,
    [Description] [nvarchar](1000) NULL,
    [Instructions] [nvarchar](2000) NULL,
    [MuscleGroups] [nvarchar](500) NULL,
    [Equipment] [nvarchar](200) NULL,
    [DifficultyLevel] [tinyint] NULL, -- 1: Başlangıç, 2: Orta, 3: İleri
    [PopularityLevel] [tinyint] NULL DEFAULT(2), -- 1: Temel, 2: Popüler, 3: İleri
    [DisplayOrder] [int] NULL DEFAULT(999), -- Sıralama için
    [IsRecommended] [bit] NULL DEFAULT(0), -- Önerilen mi?
    [UsageCount] [int] NULL DEFAULT(0), -- Kullanım sayısı (ileride)
    [IsActive] [bit] NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_CompanyExercises] PRIMARY KEY CLUSTERED ([CompanyExerciseID] ASC),
    CONSTRAINT [FK_CompanyExercises_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID]),
    CONSTRAINT [FK_CompanyExercises_ExerciseCategories] FOREIGN KEY([ExerciseCategoryID]) 
        REFERENCES [dbo].[ExerciseCategories] ([ExerciseCategoryID])
)
GO

-- PERFORMANS İNDEXLERİ (10.000+ kullanıcı için kritik)

-- ExerciseCategories indexleri
CREATE NONCLUSTERED INDEX [IX_ExerciseCategories_CategoryName] ON [dbo].[ExerciseCategories]
([CategoryName] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_ExerciseCategories_IsActive] ON [dbo].[ExerciseCategories]
([IsActive] ASC)
GO

-- SystemExercises indexleri
CREATE NONCLUSTERED INDEX [IX_SystemExercises_CategoryID] ON [dbo].[SystemExercises]
([ExerciseCategoryID] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_SystemExercises_Name] ON [dbo].[SystemExercises]
([ExerciseName] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_SystemExercises_DifficultyLevel] ON [dbo].[SystemExercises]
([DifficultyLevel] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_SystemExercises_Popularity] ON [dbo].[SystemExercises]
([PopularityLevel] ASC, [DisplayOrder] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_SystemExercises_Recommended] ON [dbo].[SystemExercises]
([IsRecommended] ASC, [PopularityLevel] ASC) WHERE [IsActive] = 1
GO

-- CompanyExercises indexleri (Multi-tenant için kritik)
CREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID_CategoryID] ON [dbo].[CompanyExercises]
([CompanyID] ASC, [ExerciseCategoryID] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID_Name] ON [dbo].[CompanyExercises]
([CompanyID] ASC, [ExerciseName] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID] ON [dbo].[CompanyExercises]
([CompanyID] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_CompanyExercises_Popularity] ON [dbo].[CompanyExercises]
([CompanyID] ASC, [PopularityLevel] ASC, [DisplayOrder] ASC) WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_CompanyExercises_Recommended] ON [dbo].[CompanyExercises]
([CompanyID] ASC, [IsRecommended] ASC, [PopularityLevel] ASC) WHERE [IsActive] = 1
GO

PRINT 'Egzersiz sistemi tabloları ve indexleri başarıyla oluşturuldu!'
PRINT 'SystemExercises: Sistem geneli egzersizler'
PRINT 'CompanyExercises: Salon özel egzersizler'
PRINT 'Popülerlik sistemi: PopularityLevel, DisplayOrder, IsRecommended alanları eklendi'
PRINT 'Performans indexleri 10.000+ kullanıcı için optimize edildi.'
